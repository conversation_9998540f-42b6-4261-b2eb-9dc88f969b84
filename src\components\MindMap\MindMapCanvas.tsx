import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import React<PERSON><PERSON>, {
  Panel,
  type Node,
  type Edge,
  type <PERSON>deChange,
  type Edge<PERSON>hange,
  ReactFlowProvider,
  Background,
  type Connection,
  useReactFlow,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { nodeTypes } from './nodes';
import { Button } from '@/components/ui/button';
import { Type, Link, AlertCircle, Edit3, Trash2, Copy, HelpCircle } from 'lucide-react';
import { toReactFlowEdge, getDefaultNodeData } from '@/utils/mindMapTransforms';
import { MindMapNode, MindMapConnection } from '@/types/MindMapsTypes';
import { NodeEditModal } from './node-edit-modal';
import { EmptyState } from '@/components/Global/EmptyState';
import { ConfirmDialog } from '../Global/ConfirmDialog';
import { useMindMapEditor } from '@/hooks/useMindMapEditor';
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';
import Modal from '../Global/Modal';

// Consolidated Context Menu Component
interface ContextMenuProps {
  x: number;
  y: number;
  nodeId: string;
  nodeType: string;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onConnect: () => void;
  onClose: () => void;
}

function ContextMenu({ x, y, onEdit, onDelete, onDuplicate, onConnect, onClose }: ContextMenuProps) {
  return (
    <div
      className="fixed z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg py-1 min-w-[160px]"
      style={{ left: x, top: y }}
      onMouseLeave={onClose}
    >
      <button
        className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
        onClick={onEdit}
      >
        <Edit3 className="w-4 h-4" />
        Edit
      </button>
      <button
        className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
        onClick={onDuplicate}
      >
        <Copy className="w-4 h-4" />
        Duplicate
      </button>
      <button
        className="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
        onClick={onConnect}
      >
        <Link className="w-4 h-4" />
        Connect
      </button>
      <div className="border-t border-gray-200 dark:border-gray-700 my-1" />
      <button
        className="w-full px-3 py-2 text-left text-sm hover:bg-red-50 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400 flex items-center gap-2"
        onClick={onDelete}
      >
        <Trash2 className="w-4 h-4" />
        Delete
      </button>
    </div>
  );
}

// Consolidated Keyboard Shortcuts Help Component
function KeyboardShortcutsHelp() {
  const [isOpen, setIsOpen] = useState(false);

  const shortcuts = [
    { key: 'Ctrl + Z', action: 'Undo' },
    { key: 'Ctrl + Y', action: 'Redo' },
    { key: 'Delete', action: 'Delete selected' },
    { key: 'Ctrl + D', action: 'Duplicate selected' },
    { key: 'Ctrl + S', action: 'Save' },
    { key: 'Escape', action: 'Cancel connection' },
    { key: 'Double Click', action: 'Edit node' },
    { key: 'Right Click', action: 'Context menu' },
  ];

  return (
    <>
      <Button
        size="sm"
        variant="ghost"
        className="fixed bottom-4 right-4 z-40"
        onClick={() => setIsOpen(true)}
      >
        <HelpCircle className="w-4 h-4" />
      </Button>

      <Modal isOpen={isOpen} onClose={() => setIsOpen(false)}>
        <div className="max-w-md">
          <div>
            <p>Keyboard Shortcuts</p>
            <div>
              Use these shortcuts to work more efficiently with your mind map.
            </div>
          </div>
          <div className="space-y-2">
            {shortcuts.map((shortcut, index) => (
              <div key={index} className="flex justify-between items-center py-1">
                <span className="text-sm text-gray-600 dark:text-gray-400">{shortcut.action}</span>
                <kbd className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 rounded border">
                  {shortcut.key}
                </kbd>
              </div>
            ))}
          </div>
        </div>
      </Modal>
    </>
  );
}

class MindMapErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="h-full w-full flex items-center justify-center theme-surface">
          <EmptyState
            type="error"
            title="Something went wrong"
            description="Failed to load the mind map. Please refresh the page."
            icon={<AlertCircle className="w-full h-full" />}
            actions={[
              {
                label: 'Refresh Page',
                onClick: () => window.location.reload(),
                variant: 'outline',
              },
            ]}
            size="md"
            animated={true}
          />
        </div>
      );
    }
    return this.props.children;
  }
}

interface MindMapCanvasInnerProps {
  isEditing?: boolean;
}

function MindMapCanvasInner({ isEditing = true }: MindMapCanvasInnerProps) {
  const reactFlowInstance = useReactFlow();

  // Use the consolidated mind map editor hook
  const {
    mindMap: currentMindMap,
    addNode,
    updateNode,
    deleteNode,
    addConnection,
    deleteConnection,
    selectNode,
    clearNodeSelection,
    isConnecting,
    connectingFrom,
    startConnecting,
    endConnecting,
    contextMenu,
    confirmDialog,
    isDeleting,
    showContextMenu,
    hideContextMenu,
    hideConfirmDialog,
    handleDeleteNode,
    handleDuplicateNode,
    nodeEdit,
    openEditModal,
    closeEditModal,
    saveNodeChanges,
  } = useMindMapEditor();

  // Apply keyboard shortcuts
  useKeyboardShortcuts(isEditing);

  const nodes: Node[] = useMemo(() => {
    if (!currentMindMap?.nodes || !Array.isArray(currentMindMap.nodes)) {
      return [];
    }

    return currentMindMap.nodes.map(node => ({
      id: node.id,
      type: node.type,
      position: node.position,
      data: {
        ...node.content,
        title: node.content?.text || node.content?.title || 'New Node',
        text: node.content?.text || node.content?.title || 'New Node',
        isConnecting,
        isConnectingFrom: connectingFrom === node.id,
      },
      selected: node.selected,
      dragging: node.dragging,
    }));
  }, [currentMindMap, isConnecting, connectingFrom]);

  const edges: Edge[] = useMemo(() => {
    if (!currentMindMap?.connections || !Array.isArray(currentMindMap.connections)) {
      return [];
    }
    return currentMindMap.connections.map(toReactFlowEdge);
  }, [currentMindMap?.connections]);

  // FIXED: onNodesChange function - this was the main issue
  const onNodesChange = useCallback(
    (changes: NodeChange[]) => {
      if (!isEditing) return;

      changes.forEach(change => {
        switch (change.type) {
          case 'position':
            if (change.position && change.positionAbsolute) {
              updateNode(change.id, { position: change.positionAbsolute });
            }
            break;
          case 'select':
            if (change.selected) {
              selectNode(change.id);
            } else {
              clearNodeSelection();
            }
            break;
          case 'remove':
            deleteNode(change.id);
            break;
          case 'dimensions':
            if (change.dimensions) {
              updateNode(change.id, {
                size: {
                  width: change.dimensions.width,
                  height: change.dimensions.height,
                },
              });
            }
            break;
        }
      });
    },
    [isEditing, updateNode, selectNode, clearNodeSelection, deleteNode]
  );

  const onEdgesChange = useCallback(
    (changes: EdgeChange[]) => {
      if (!isEditing) return;

      changes.forEach(change => {
        if (change.type === 'remove') {
          deleteConnection(change.id);
        }
      });
    },
    [isEditing, deleteConnection]
  );

  const addTextNode = useCallback(() => {
    const viewport = reactFlowInstance.getViewport();
    const nodeId = `node-${Date.now()}`;
    const defaultData = getDefaultNodeData('text');

    const position = {
      x: -viewport.x / viewport.zoom + (Math.random() * 200 - 100),
      y: -viewport.y / viewport.zoom + (Math.random() * 200 - 100),
    };

    const newNode: MindMapNode = {
      id: nodeId,
      type: 'text',
      position,
      size: { width: 200, height: 100 },
      content: {
        text: 'New Text Node',
        ...defaultData,
      },
      style: {
        borderColor: '#cccccc',
        borderWidth: 1,
        borderRadius: 8,
        opacity: 1,
        shadow: false,
      },
      parentId: undefined,
      childIds: [],
      collapsed: false,
      zIndex: 1,
      selected: false,
      dragging: false,
    };

    addNode(newNode);

    setTimeout(() => {
      selectNode(nodeId);
      reactFlowInstance.setCenter(position.x, position.y, { zoom: 1 });
    }, 50);
  }, [addNode, selectNode, reactFlowInstance]);

  // Optimized event handlers
  const onNodeDoubleClick = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      if (isEditing) openEditModal(node.id);
    },
    [isEditing, openEditModal]
  );

  const onNodeContextMenu = useCallback(
    (event: React.MouseEvent, node: Node) => {
      if (isEditing) {
        event.preventDefault();
        showContextMenu(event.clientX, event.clientY, node.id, node.type || 'default');
      }
    },
    [isEditing, showContextMenu]
  );

  const onNodeClick = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      if (!isEditing) return;

      if (isConnecting && connectingFrom && connectingFrom !== node.id) {
        const connectionId = `connection-${connectingFrom}-${node.id}-${Date.now()}`;
        const newConnection: MindMapConnection = {
          id: connectionId,
          sourceNodeId: connectingFrom,
          targetNodeId: node.id,
          type: 'curved',
          style: {
            stroke: '#3b82f6',
            strokeWidth: 2,
            animated: false,
          },
        };
        addConnection(newConnection);
        endConnecting();
      }
    },
    [isEditing, isConnecting, connectingFrom, addConnection, endConnecting]
  );

  const onConnect = useCallback(
    (connection: Connection) => {
      if (!isEditing || !connection.source || !connection.target) return;

      const connectionId = `connection-${connection.source}-${connection.target}-${Date.now()}`;
      const newConnection: MindMapConnection = {
        id: connectionId,
        sourceNodeId: connection.source,
        targetNodeId: connection.target,
        type: 'curved',
        style: {
          stroke: '#3b82f6',
          strokeWidth: 2,
          animated: false,
        },
      };
      addConnection(newConnection);
    },
    [isEditing, addConnection]
  );

  return (
    <div className="h-full w-full relative">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        onNodesChange={isEditing ? onNodesChange : undefined} // FIXED: Re-enabled onNodesChange
        onEdgesChange={isEditing ? onEdgesChange : undefined}
        onNodeDoubleClick={onNodeDoubleClick}
        onNodeContextMenu={onNodeContextMenu}
        onNodeClick={onNodeClick}
        onConnect={onConnect}
        fitView
        snapToGrid={isEditing}
        snapGrid={[20, 20]}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        connectionLineStyle={{ stroke: '#3b82f6', strokeWidth: 2 }}
        deleteKeyCode={isEditing ? 'Delete' : null}
        minZoom={0.2}
        maxZoom={1.5}
        nodesDraggable={isEditing}
        nodesConnectable={isEditing}
        elementsSelectable={true}
        panOnDrag={true}
        zoomOnScroll={true}
        preventScrolling={true}
        proOptions={{ hideAttribution: true }}
      >
        <Background />

        {/* Optimized Toolbar */}
        {isEditing && (
          <Panel position="top-right">
            <div className="flex gap-2">
              <Button size="sm" variant="outline" onClick={addTextNode}>
                <Type className="h-4 w-4 mr-1" />
                Text
              </Button>
              <Button
                size="sm"
                variant={isConnecting ? 'default' : 'outline'}
                onClick={isConnecting ? endConnecting : undefined}
              >
                <Link className="h-4 w-4 mr-1" />
                {isConnecting ? 'Cancel' : 'Connect'}
              </Button>
            </div>
          </Panel>
        )}
      </ReactFlow>

      {/* Consolidated Modals and UI */}
      <NodeEditModal
        isOpen={nodeEdit.isModalOpen}
        nodeId={nodeEdit.editingNodeId}
        nodeType={nodeEdit.editingNodeType}
        nodeData={nodeEdit.editingNodeData}
        onSave={saveNodeChanges}
        onClose={closeEditModal}
      />

      {contextMenu && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          nodeId={contextMenu.nodeId}
          nodeType={contextMenu.nodeType}
          onEdit={() => {
            openEditModal(contextMenu.nodeId);
            hideContextMenu();
          }}
          onDelete={() => handleDeleteNode(contextMenu.nodeId)}
          onDuplicate={() => handleDuplicateNode(contextMenu.nodeId)}
          onConnect={() => {
            startConnecting(contextMenu.nodeId);
            hideContextMenu();
          }}
          onClose={hideContextMenu}
        />
      )}

      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={hideConfirmDialog}
        title={confirmDialog.title}
        description={confirmDialog.description}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDialog.onConfirm}
        variant="destructive"
        loading={isDeleting}
      />

      {isEditing && <KeyboardShortcutsHelp />}
    </div>
  );
}

export default function MindMapCanvas({ isEditing = true }: { isEditing?: boolean }) {
  return (
    <MindMapErrorBoundary>
      <ReactFlowProvider>
        <MindMapCanvasInner isEditing={isEditing} />
      </ReactFlowProvider>
    </MindMapErrorBoundary>
  );
}
